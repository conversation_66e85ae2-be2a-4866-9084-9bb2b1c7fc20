# Discord Service Setup Guide

## Overview
The ZeroDateStrat Discord service provides real-time notifications and bot interactions for the trading system.

## Required Environment Variables

Set these environment variables for secure operation:

```bash
# Discord Bot Token (Main Trading Bot)
DISCORD_BOT_TOKEN=your_main_bot_token_here

# ChatGPT Discord Bot Token (Separate Bot)
CHATGPT_BOT_TOKEN=your_chatgpt_bot_token_here

# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here
```

## Discord Bot Setup

### 1. Create Discord Application
1. Go to https://discord.com/developers/applications
2. Click "New Application"
3. Name it "ZeroDateStrat Trading Bot"
4. Go to "Bot" section
5. Click "Add Bot"
6. Copy the bot token and set as `DISCORD_BOT_TOKEN`

### 2. Create ChatGPT Bot (Optional)
1. Create another application named "ChatGPT Assistant"
2. Add bot and copy token
3. Set as `CHATGPT_BOT_TOKEN`

### 3. Enable Required Intents (CRITICAL)
**This step is required to fix the "Disallowed intent(s)" error:**

1. Go to your Discord application in the Developer Portal
2. Navigate to the "Bot" section
3. Scroll down to "Privileged Gateway Intents"
4. Enable these intents:
   - ✅ **Message Content Intent** (Required for reading message content)
   - ✅ **Server Members Intent** (Optional, for member-related features)
   - ✅ **Presence Intent** (Optional, for presence updates)

### 4. Bot Permissions
Required permissions for both bots:
- Send Messages
- Use Slash Commands
- Read Message History
- Embed Links
- Attach Files

### 5. Invite Bots to Server
Use this URL format (replace CLIENT_ID with your bot's client ID):
```
https://discord.com/api/oauth2/authorize?client_id=CLIENT_ID&permissions=2147485696&scope=bot%20applications.commands
```

## Configuration

### Channel Setup
1. Get your Discord channel ID (enable Developer Mode in Discord)
2. Right-click the channel and "Copy ID"
3. Update `ChannelId` in appsettings.json

### Features
- **Real-time Alerts**: Risk alerts, trade notifications
- **Portfolio Reports**: Morning and end-of-day summaries
- **Interactive Commands**: Status, portfolio, alerts, emergency stop
- **ChatGPT Integration**: AI assistance via Discord

## Commands

### Text Commands (prefix with !)
- `!status` - Get system status
- `!portfolio` - View portfolio
- `!alerts` - View recent alerts
- `!positions` - View current positions
- `!metrics` - View risk metrics
- `!stop` - Emergency stop trading
- `!help` - Show help

### Slash Commands
- `/status` - Get system status
- `/portfolio` - View portfolio
- `/alerts` - View alerts
- `/stop` - Emergency stop

### ChatGPT Commands
- `!chatgpt [question]` - Ask ChatGPT
- `@ChatGPTBot [question]` - Mention the bot

## Security Notes

1. **Never commit bot tokens to source control**
2. **Use environment variables for all secrets**
3. **Regularly rotate bot tokens**
4. **Limit bot permissions to minimum required**
5. **Monitor bot usage in Discord audit logs**

## Troubleshooting

### Common Issues

1. **"Disallowed intent(s)" Error (Error 4014)**
   - **Cause**: Message Content Intent not enabled in Discord Developer Portal
   - **Solution**:
     1. Go to Discord Developer Portal → Your Application → Bot
     2. Enable "Message Content Intent" under Privileged Gateway Intents
     3. Save changes and restart your application
   - **This is the most common issue and must be fixed first**

2. **Bot not responding**
   - Check bot token is valid
   - Verify bot has required permissions
   - Check channel ID is correct
   - Ensure Message Content Intent is enabled

3. **Connection failures**
   - Check internet connectivity
   - Verify Discord API status
   - Review logs for specific errors
   - Verify bot token hasn't expired

4. **Missing messages**
   - Ensure Message Content Intent is enabled
   - Check bot permissions in channel
   - Verify channel ID matches configuration

### Logs
Check application logs for Discord-related errors:
```
logs/zerodtestrat-[date].txt
```

## Testing

Run Discord error test:
```bash
dotnet run discord-error-test
```

This will test Discord connectivity and error notification functionality.
